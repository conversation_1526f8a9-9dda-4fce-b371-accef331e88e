# PowerShell script to upload multiple test images for modal testing

$BACKEND_URL = "http://localhost:8001"
$API_BASE = "$BACKEND_URL/api"

Write-Host "🖼️ Uploading Test Images for Modal Testing" -ForegroundColor Cyan
Write-Host "=" * 50

# Login to get token
$loginBody = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "$API_BASE/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
$token = $loginResponse.access_token
$headers = @{ Authorization = "Bearer $token" }

Write-Host "✅ Logged in successfully" -ForegroundColor Green

# Create different test images (simple colored squares)
$testImages = @(
    @{
        name = "red_square.png"
        color = "red"
        base64 = "iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8z8BQz0AEYBxVSF+FABJADveWkH6oAAAAAElFTkSuQmCC"
    },
    @{
        name = "blue_square.png"
        color = "blue"
        base64 = "iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mNkYPhfz0AEYBxVSF+FAAhKDveksOjmAAAAAElFTkSuQmCC"
    },
    @{
        name = "green_square.png"
        color = "green"
        base64 = "iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mNk+M9Qz0AEYBxVSF+FACdKDvfrLvl2AAAAAElFTkSuQmCC"
    }
)

foreach ($image in $testImages) {
    Write-Host "`nUploading $($image.name)..." -ForegroundColor Yellow
    
    try {
        # Create image file
        $imageBytes = [System.Convert]::FromBase64String($image.base64)
        $tempPath = "temp_$($image.name)"
        [System.IO.File]::WriteAllBytes($tempPath, $imageBytes)
        
        # Create multipart form data
        $boundary = [System.Guid]::NewGuid().ToString()
        $LF = "`r`n"
        
        $fileBytes = [System.IO.File]::ReadAllBytes($tempPath)
        $fileContent = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)
        
        $bodyLines = (
            "--$boundary",
            "Content-Disposition: form-data; name=`"file`"; filename=`"$($image.name)`"",
            "Content-Type: image/png$LF",
            $fileContent,
            "--$boundary--$LF"
        ) -join $LF
        
        $uploadResponse = Invoke-RestMethod -Uri "$API_BASE/adimages/upload" -Method POST -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary" -Headers $headers
        
        Write-Host "✅ $($image.name) uploaded successfully" -ForegroundColor Green
        Write-Host "   Image ID: $($uploadResponse.id)" -ForegroundColor Gray
        
        # Clean up temp file
        Remove-Item $tempPath -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ Failed to upload $($image.name)" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
        
        # Clean up temp file
        Remove-Item $tempPath -ErrorAction SilentlyContinue
    }
}

# Check final image count
Write-Host "`nChecking final image count..." -ForegroundColor Yellow
try {
    $images = Invoke-RestMethod -Uri "$API_BASE/adimages" -Method GET -Headers $headers
    Write-Host "✅ Total images available: $($images.Count)" -ForegroundColor Green
    
    Write-Host "`nImage list:" -ForegroundColor Cyan
    foreach ($img in $images) {
        Write-Host "  - $($img.original_filename) ($($img.file_size) bytes)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Failed to retrieve image list" -ForegroundColor Red
}

Write-Host "`n" + "=" * 50
Write-Host "🎉 Test images uploaded! You can now test the modal slider." -ForegroundColor Cyan
Write-Host "💡 Features to test:" -ForegroundColor Yellow
Write-Host "   • Click any image to open modal" -ForegroundColor Gray
Write-Host "   • Auto-slide every 4 seconds" -ForegroundColor Gray
Write-Host "   • Arrow keys for navigation" -ForegroundColor Gray
Write-Host "   • Spacebar to pause/resume" -ForegroundColor Gray
Write-Host "   • 'I' key to toggle metadata" -ForegroundColor Gray
Write-Host "   • Escape to close modal" -ForegroundColor Gray
Write-Host "   • Click thumbnails or dots to jump" -ForegroundColor Gray
