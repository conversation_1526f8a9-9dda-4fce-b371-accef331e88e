import asyncio
from motor.motor_asyncio import AsyncIOMotorClient

async def check_blogs():
    client = AsyncIOMotorClient('mongodb://vibrantyoga:<PERSON><PERSON><PERSON>@82.29.165.77:27017/vibrantyoga')
    db = client['vibrantyoga']
    
    blogs = await db.blogs.find({}).to_list(100)
    print(f'Total blogs: {len(blogs)}')
    
    for blog in blogs:
        print(f'Blog: {blog.get("title", "No title")} - Status: {blog.get("status", "No status")}')
    
    client.close()

if __name__ == "__main__":
    asyncio.run(check_blogs())
