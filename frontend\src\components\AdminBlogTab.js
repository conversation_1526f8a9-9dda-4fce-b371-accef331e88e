// AdminBlogTab.js
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  FileText, 
  Save, 
  Eye, 
  Upload, 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Code,
  Link,
  Image,
  Table,
  Minus,
  Type,
  Palette,
  Highlighter,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  Undo,
  Redo,
  Maximize,
  Minimize,
  Search,
  RotateCcw,
  Trash2,
  Edit,
  X,
  Download,
  Printer,
  Indent,
  Outdent,
  CheckSquare,
  Square,
  MoreHorizontal,
  Smile,
  AlignVerticalJustifyStart,
  AlignVerticalJustifyCenter,
  AlignVerticalJustifyEnd
} from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import TextAlign from '@tiptap/extension-text-align';
import UnderlineExtension from '@tiptap/extension-underline';
import SubscriptExtension from '@tiptap/extension-subscript';
import SuperscriptExtension from '@tiptap/extension-superscript';
import Strike from '@tiptap/extension-strike';
import LinkExtension from '@tiptap/extension-link';
import ImageExtension from '@tiptap/extension-image';
import TableExtension from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import CharacterCount from '@tiptap/extension-character-count';
import Placeholder from '@tiptap/extension-placeholder';
import Focus from '@tiptap/extension-focus';
import Typography from '@tiptap/extension-typography';
import Gapcursor from '@tiptap/extension-gapcursor';
import ListItem from '@tiptap/extension-list-item';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import Dropcursor from '@tiptap/extension-dropcursor';
import '../tiptap-editor.css';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const AdminBlogTab = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [form, setForm] = useState({
    title: '',
    content: '',
    status: 'draft',
    author_name: '',
    category: '',
    tags: [],
    featured_image: '',
    excerpt: ''
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showWordCount, setShowWordCount] = useState(true);
  const [editingBlog, setEditingBlog] = useState(null);
  const [tagInput, setTagInput] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [showFindReplace, setShowFindReplace] = useState(false);
  const [findText, setFindText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showSpecialChars, setShowSpecialChars] = useState(false);

  // Category management state
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [categoryForm, setCategoryForm] = useState({ name: '', description: '', color: '' });
  const [editingCategory, setEditingCategory] = useState(null);
  const [categoryLoading, setCategoryLoading] = useState(false);

  // Auto-save functionality
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const autoSaveInterval = useRef(null);
  const lastSavedContent = useRef('');

  // Common emojis
  const commonEmojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕'];

  // Special characters
  const specialChars = ['©', '®', '™', '§', '¶', '†', '‡', '•', '‰', '′', '″', '‴', '※', '‼', '⁇', '⁈', '⁉', '⁏', '؟', '؞', '؍', '؎', '؏', 'ؐ', 'ؑ', 'ؒ', 'ؓ', 'ؔ', '؛', '؜', '؝', '؞', '؟', '؀', '؁', '؂', '؃', '؄', '؅', '؆', '؇', '؈', '؉', '؊', '؋', '،', '؍', '؎', '؏', 'ؐ', 'ؑ', 'ؒ', 'ؓ', 'ؔ', '؛', '؜', '؝', '؞', '؟', '؀', '؁', '؂', '؃', '؄', '؅', '؆', '؇', '؈', '؉', '؊', '؋', '،', '؍', '؎', '؏', 'ؐ', 'ؑ', 'ؒ', 'ؓ', 'ؔ', '؛', '؜', '؝', '؞', '؟', '←', '↑', '→', '↓', '↔', '↕', '↖', '↗', '↘', '↙', '↚', '↛', '↜', '↝', '↞', '↟', '↠', '↡', '↢', '↣', '↤', '↥', '↦', '↧', '↨', '↩', '↪', '↫', '↬', '↭', '↮', '↯', '↰', '↱', '↲', '↳', '↴', '↵', '↶', '↷', '↸', '↹', '↺', '↻', '↼', '↽', '↾', '↿', '⇀', '⇁', '⇂', '⇃', '⇄', '⇅', '⇆', '⇇', '⇈', '⇉', '⇊', '⇋', '⇌', '⇍', '⇎', '⇏', '⇐', '⇑', '⇒', '⇓', '⇔', '⇕', '⇖', '⇗', '⇘', '⇙', '⇚', '⇛', '⇜', '⇝', '⇞', '⇟', '⇠', '⇡', '⇢', '⇣', '⇤', '⇥', '⇦', '⇧', '⇨', '⇩', '⇪', '⇫', '⇬', '⇭', '⇮', '⇯', '⇰', '⇱', '⇲', '⇳', '⇴', '⇵', '⇶', '⇷', '⇸', '⇹', '⇺', '⇻', '⇼', '⇽', '⇾', '⇿'];

  // Font families for the editor
  const fontFamilies = [
    'Arial, sans-serif',
    'Georgia, serif',
    'Times New Roman, serif',
    'Helvetica, sans-serif',
    'Verdana, sans-serif',
    'Courier New, monospace',
    'Trebuchet MS, sans-serif',
    'Palatino, serif',
    'Garamond, serif',
    'Comic Sans MS, cursive'
  ];

  // Font sizes
  const fontSizes = ['8pt', '9pt', '10pt', '11pt', '12pt', '14pt', '16pt', '18pt', '20pt', '24pt', '28pt', '32pt', '36pt', '48pt', '72pt'];

  // Custom font size extension
  const FontSize = TextStyle.extend({
    addAttributes() {
      return {
        fontSize: {
          default: null,
          parseHTML: element => element.style.fontSize,
          renderHTML: attributes => {
            if (!attributes.fontSize) {
              return {}
            }
            return {
              style: `font-size: ${attributes.fontSize}`,
            }
          },
        },
      }
    },
  });

  // TipTap editor configuration
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: {
          depth: 100,
        },
      }),
      TextStyle,
      FontSize,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      UnderlineExtension,
      SubscriptExtension,
      SuperscriptExtension,
      Strike,
      LinkExtension.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
      }),
      ImageExtension.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      TableExtension.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      HorizontalRule,
      CharacterCount,
      Placeholder.configure({
        placeholder: 'Start writing your blog post content here...',
      }),
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
      Typography,
      Gapcursor,
      ListItem,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Dropcursor,
    ],
    content: form.content,
    onUpdate: ({ editor }) => {
      const newContent = editor.getHTML();
      setForm(prev => ({ ...prev, content: newContent }));
      
      // Auto-save functionality
      if (autoSaveEnabled && newContent !== lastSavedContent.current) {
        clearTimeout(autoSaveInterval.current);
        autoSaveInterval.current = setTimeout(() => {
          handleAutoSave(newContent);
        }, 2000); // Auto-save after 2 seconds of inactivity
      }
    },
    editorProps: {
      attributes: {
        class: 'min-h-[500px] p-4 focus:outline-none prose prose-lg max-w-none',
      },
    },
  });

  useEffect(() => {
    fetchBlogs();
    fetchCategories();
    fetchTags();
    return () => {
      if (autoSaveInterval.current) {
        clearTimeout(autoSaveInterval.current);
      }
    };
  }, []);

  // Update editor content when form content changes
  useEffect(() => {
    if (editor && form.content !== editor.getHTML()) {
      editor.commands.setContent(form.content);
    }
  }, [form.content, editor]);

  // Add custom commands to editor
  useEffect(() => {
    if (editor) {
      editor.commands.setFontSize = (fontSize) => {
        return editor.chain().focus().setMark('textStyle', { fontSize }).run();
      };

      editor.commands.unsetFontSize = () => {
        return editor.chain().focus().unsetMark('textStyle').run();
      };
    }
  }, [editor]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+S to save
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        handleSave('draft');
      }
      // Ctrl+Shift+S to publish
      if (e.ctrlKey && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        handleSave('published');
      }
      // Ctrl+F to find
      if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        setShowFindReplace(true);
      }
      // Escape to close find/replace
      if (e.key === 'Escape') {
        setShowFindReplace(false);
      }
      // F11 for fullscreen
      if (e.key === 'F11') {
        e.preventDefault();
        setIsFullscreen(!isFullscreen);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen]);

  const handleFind = () => {
    if (!findText || !editor) return;

    // Simple find functionality - highlight text
    const content = editor.getHTML();
    const regex = new RegExp(findText, 'gi');
    const highlightedContent = content.replace(regex, `<mark>$&</mark>`);
    editor.commands.setContent(highlightedContent);
  };

  const handleReplace = () => {
    if (!findText || !editor) return;

    const content = editor.getHTML();
    const regex = new RegExp(findText, 'gi');
    const replacedContent = content.replace(regex, replaceText);
    editor.commands.setContent(replacedContent);
    setForm(prev => ({ ...prev, content: replacedContent }));
  };

  const handleReplaceAll = () => {
    if (!findText || !editor) return;

    const content = editor.getHTML();
    const regex = new RegExp(findText, 'gi');
    const replacedContent = content.replace(regex, replaceText);
    editor.commands.setContent(replacedContent);
    setForm(prev => ({ ...prev, content: replacedContent }));
  };

  const handleExportHTML = () => {
    if (!form.content) {
      toast.error('No content to export');
      return;
    }

    const blob = new Blob([form.content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${form.title || 'blog-post'}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('HTML exported successfully');
  };

  const handlePrint = () => {
    if (!form.content) {
      toast.error('No content to print');
      return;
    }

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${form.title || 'Blog Post'}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
            h1, h2, h3, h4, h5, h6 { margin-top: 1.5em; margin-bottom: 0.5em; }
            p { margin: 0.75em 0; }
            blockquote { border-left: 4px solid #ccc; margin: 1em 0; padding-left: 1em; }
            table { border-collapse: collapse; width: 100%; }
            td, th { border: 1px solid #ddd; padding: 8px; }
            th { background-color: #f2f2f2; }
            img { max-width: 100%; height: auto; }
          </style>
        </head>
        <body>
          <h1>${form.title || 'Blog Post'}</h1>
          ${form.content}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const insertEmoji = (emoji) => {
    editor.chain().focus().insertContent(emoji).run();
    setShowEmojiPicker(false);
  };

  const insertSpecialChar = (char) => {
    editor.chain().focus().insertContent(char).run();
    setShowSpecialChars(false);
  };

  const fetchBlogs = async () => {
    setLoading(true);
    try {
      const res = await axios.get(`${API}/blogs`);
      setBlogs(res.data);
    } catch (error) {
      toast.error('Failed to fetch blog posts');
      console.error('Error fetching blogs:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const res = await axios.get(`${API}/categories`);
      setCategories(res.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to fetch categories');
    }
  };

  const fetchTags = async () => {
    try {
      const res = await axios.get(`${API}/tags`);
      setTags(res.data);
    } catch (error) {
      console.error('Error fetching tags:', error);
      toast.error('Failed to fetch tags');
    }
  };

  const handleCategorySubmit = async (e) => {
    e.preventDefault();
    if (!categoryForm.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    setCategoryLoading(true);
    try {
      if (editingCategory) {
        await axios.put(`${API}/categories/${editingCategory.id}`, categoryForm);
        toast.success('Category updated successfully');
      } else {
        await axios.post(`${API}/categories`, categoryForm);
        toast.success('Category created successfully');
      }

      setCategoryForm({ name: '', description: '', color: '' });
      setEditingCategory(null);
      fetchCategories();
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Failed to save category';
      toast.error(errorMessage);
    } finally {
      setCategoryLoading(false);
    }
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setCategoryForm({
      name: category.name,
      description: category.description || '',
      color: category.color || ''
    });
  };

  const handleDeleteCategory = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category?')) return;

    try {
      await axios.delete(`${API}/categories/${categoryId}`);
      toast.success('Category deleted successfully');
      fetchCategories();
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Failed to delete category';
      toast.error(errorMessage);
    }
  };

  const resetCategoryForm = () => {
    setCategoryForm({ name: '', description: '', color: '' });
    setEditingCategory(null);
  };

  const handleAutoSave = async (content) => {
    if (!editingBlog || !form.title.trim()) return;
    
    try {
      await axios.put(`${API}/blogs/${editingBlog.id}`, {
        ...form,
        content,
        status: 'draft'
      });
      lastSavedContent.current = content;
      toast.success('Auto-saved', { duration: 1000 });
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async (status = 'draft') => {
    if (!form.title.trim()) {
      toast.error('Please enter a blog title');
      return;
    }
    
    if (!form.content.trim()) {
      toast.error('Please enter blog content');
      return;
    }

    setSaving(true);
    try {
      const blogData = { ...form, status };
      
      if (editingBlog) {
        await axios.put(`${API}/blogs/${editingBlog.id}`, blogData);
        toast.success(`Blog ${status === 'published' ? 'published' : 'saved'} successfully`);
      } else {
        await axios.post(`${API}/blogs`, blogData);
        toast.success(`Blog ${status === 'published' ? 'published' : 'created'} successfully`);
      }
      
      lastSavedContent.current = form.content;
      fetchBlogs();
      
      if (status === 'published') {
        resetForm();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || `Failed to ${status === 'published' ? 'publish' : 'save'} blog`;
      toast.error(errorMessage);
      console.error('Error saving blog:', error);
    } finally {
      setSaving(false);
    }
  };

  const resetForm = () => {
    setForm({
      title: '',
      content: '',
      status: 'draft',
      author_name: '',
      category: '',
      tags: [],
      featured_image: '',
      excerpt: ''
    });
    setEditingBlog(null);
    if (editor) {
      editor.commands.setContent('');
    }
    lastSavedContent.current = '';
    setTagInput('');
  };

  const addTag = () => {
    const tag = tagInput.trim();
    if (tag && !form.tags.includes(tag)) {
      setForm(prev => ({ ...prev, tags: [...prev.tags, tag] }));
      setTagInput('');
    }
  };

  const removeTag = (indexToRemove) => {
    setForm(prev => ({
      ...prev,
      tags: prev.tags.filter((_, index) => index !== indexToRemove)
    }));
  };

  const handleTagKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTag();
    }
  };

  const handleEdit = (blog) => {
    setEditingBlog(blog);
    setForm({
      title: blog.title,
      content: blog.content,
      status: blog.status,
      author_name: blog.author_name || '',
      category: blog.category || '',
      tags: blog.tags || [],
      featured_image: blog.featured_image || '',
      excerpt: blog.excerpt || ''
    });
    if (editor) {
      editor.commands.setContent(blog.content);
    }
    lastSavedContent.current = blog.content;
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this blog post?')) return;

    try {
      await axios.delete(`${API}/blogs/${id}`);
      toast.success('Blog post deleted successfully');
      fetchBlogs();

      if (editingBlog && editingBlog.id === id) {
        resetForm();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Failed to delete blog post';
      toast.error(errorMessage);
      console.error('Error deleting blog:', error);
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a valid image file (JPG, PNG, GIF, or WebP)');
      e.target.value = '';
      return;
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast.error('File size must be less than 10MB');
      e.target.value = '';
      return;
    }

    setImageUploading(true);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post(`${API}/adimages/upload`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      const imageUrl = `${BACKEND_URL}/${response.data.file_path}`;
      editor.chain().focus().setImage({ src: imageUrl }).run();
      toast.success('Image uploaded and inserted successfully');
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Image upload failed';
      toast.error(errorMessage);
      console.error('Error uploading image:', error);
    } finally {
      setImageUploading(false);
      e.target.value = '';
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white overflow-auto' : ''}`}>
      <div className={`${isFullscreen ? 'p-6' : ''}`}>
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <FileText className="w-5 h-5" /> Blog Management
        </h2>
      
      {/* Blog Editor Form */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">
            {editingBlog ? 'Edit Blog Post' : 'Create New Blog Post'}
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setAutoSaveEnabled(!autoSaveEnabled)}
              className={`px-3 py-1 text-sm rounded ${autoSaveEnabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`}
            >
              Auto-save: {autoSaveEnabled ? 'ON' : 'OFF'}
            </button>
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-600 hover:text-gray-800 rounded"
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Title Input */}
        <div className="mb-4">
          <label htmlFor="blog-title" className="block mb-2 font-medium">Blog Title *</label>
          <input
            id="blog-title"
            type="text"
            name="title"
            value={form.title}
            onChange={handleInputChange}
            placeholder="Enter your blog post title..."
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
            aria-required="true"
            aria-describedby="title-help"
          />
          <div id="title-help" className="sr-only">
            Enter a descriptive title for your blog post
          </div>
        </div>

        {/* Author Name */}
        <div className="mb-4">
          <label htmlFor="author-name" className="block mb-2 font-medium">Author Name</label>
          <input
            id="author-name"
            type="text"
            name="author_name"
            value={form.author_name}
            onChange={handleInputChange}
            placeholder="Enter author name..."
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {/* Category */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="category" className="block font-medium">Category</label>
            <button
              type="button"
              onClick={() => setShowCategoryManager(!showCategoryManager)}
              className="text-sm text-purple-600 hover:text-purple-700 font-medium"
            >
              Manage Categories
            </button>
          </div>
          <select
            id="category"
            name="category"
            value={form.category}
            onChange={handleInputChange}
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">Select a category</option>
            {categories.map((category) => (
              <option key={category.id} value={category.name}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Featured Image URL */}
        <div className="mb-4">
          <label htmlFor="featured-image" className="block mb-2 font-medium">Featured Image URL</label>
          <input
            id="featured-image"
            type="text"
            name="featured_image"
            value={form.featured_image}
            onChange={handleInputChange}
            placeholder="Enter image URL or upload an image..."
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {/* Excerpt */}
        <div className="mb-4">
          <label htmlFor="excerpt" className="block mb-2 font-medium">Excerpt</label>
          <textarea
            id="excerpt"
            name="excerpt"
            value={form.excerpt}
            onChange={handleInputChange}
            placeholder="Enter a short excerpt for your blog post..."
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            rows="3"
          ></textarea>
          <p className="text-sm text-gray-500 mt-1">
            A short summary of your post. If left empty, it will be generated automatically.
          </p>
        </div>

        {/* Tags */}
        <div className="mb-4">
          <label className="block mb-2 font-medium">Tags</label>
          <div className="flex flex-wrap gap-2 mb-2">
            {form.tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="ml-2 text-purple-600 hover:text-purple-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleTagKeyDown}
              placeholder="Add tags (press Enter or comma to add)"
              className="flex-1 border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <button
              type="button"
              onClick={addTag}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              Add Tag
            </button>
          </div>
        </div>

        {/* Category Management Modal */}
        {showCategoryManager && (
          <div className="mb-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium">Category Management</h4>
              <button
                onClick={() => {
                  setShowCategoryManager(false);
                  resetCategoryForm();
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Category Form */}
            <form onSubmit={handleCategorySubmit} className="mb-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Category Name *</label>
                  <input
                    type="text"
                    value={categoryForm.name}
                    onChange={(e) => setCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter category name"
                    className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Description</label>
                  <input
                    type="text"
                    value={categoryForm.description}
                    onChange={(e) => setCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter description"
                    className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Color</label>
                  <input
                    type="color"
                    value={categoryForm.color || '#6366f1'}
                    onChange={(e) => setCategoryForm(prev => ({ ...prev, color: e.target.value }))}
                    className="w-full h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  type="submit"
                  disabled={categoryLoading}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
                >
                  {categoryLoading ? 'Saving...' : (editingCategory ? 'Update Category' : 'Create Category')}
                </button>
                {editingCategory && (
                  <button
                    type="button"
                    onClick={resetCategoryForm}
                    className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                )}
              </div>
            </form>

            {/* Categories List */}
            <div className="max-h-60 overflow-y-auto">
              <h5 className="font-medium mb-2">Existing Categories</h5>
              <div className="space-y-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between p-3 bg-white rounded border">
                    <div className="flex items-center gap-3">
                      {category.color && (
                        <div
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: category.color }}
                        ></div>
                      )}
                      <div>
                        <div className="font-medium">{category.name}</div>
                        {category.description && (
                          <div className="text-sm text-gray-500">{category.description}</div>
                        )}
                        <div className="text-xs text-gray-400">{category.post_count} posts</div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEditCategory(category)}
                        className="p-1 text-blue-600 hover:text-blue-800"
                        title="Edit category"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category.id)}
                        className="p-1 text-red-600 hover:text-red-800"
                        title="Delete category"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Rich Text Editor Toolbar */}
        <div className="mb-4">
          <label className="block mb-2 font-medium">Content *</label>

          {/* Advanced Toolbar */}
          {editor && (
            <div className="border border-gray-300 rounded-t-md bg-gray-50 p-2">
              {/* First Row - Basic Formatting */}
              <div className="flex flex-wrap items-center gap-1 mb-2 pb-2 border-b border-gray-200">
                {/* Undo/Redo */}
                <button
                  onClick={() => editor.chain().focus().undo().run()}
                  disabled={!editor.can().undo()}
                  className="p-1.5 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Undo (Ctrl+Z)"
                >
                  <Undo className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().redo().run()}
                  disabled={!editor.can().redo()}
                  className="p-1.5 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Redo (Ctrl+Y)"
                >
                  <Redo className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Font Family */}
                <select
                  value={editor.getAttributes('textStyle').fontFamily || 'Arial, sans-serif'}
                  onChange={(e) => editor.chain().focus().setFontFamily(e.target.value).run()}
                  className="px-2 py-1 text-sm border border-gray-300 rounded"
                >
                  {fontFamilies.map((font) => (
                    <option key={font} value={font} style={{ fontFamily: font }}>
                      {font.split(',')[0]}
                    </option>
                  ))}
                </select>

                {/* Font Size */}
                <select
                  value={editor.getAttributes('textStyle').fontSize || '12pt'}
                  onChange={(e) => {
                    const size = e.target.value;
                    if (size === '12pt') {
                      editor.chain().focus().unsetFontSize().run();
                    } else {
                      editor.chain().focus().setFontSize(size).run();
                    }
                  }}
                  className="px-2 py-1 text-sm border border-gray-300 rounded"
                >
                  {fontSizes.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Basic Formatting */}
                <button
                  onClick={() => editor.chain().focus().toggleBold().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('bold') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Bold (Ctrl+B)"
                >
                  <Bold className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().toggleItalic().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('italic') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Italic (Ctrl+I)"
                >
                  <Italic className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().toggleUnderline().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('underline') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Underline (Ctrl+U)"
                >
                  <Underline className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().toggleStrike().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('strike') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Strikethrough"
                >
                  <Strikethrough className="w-4 h-4" />
                </button>

                {/* Subscript/Superscript */}
                <button
                  onClick={() => editor.chain().focus().toggleSubscript().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('subscript') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Subscript"
                >
                  <SubscriptIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().toggleSuperscript().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('superscript') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Superscript"
                >
                  <SuperscriptIcon className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Text Color */}
                <div className="relative">
                  <input
                    type="color"
                    onChange={(e) => editor.chain().focus().setColor(e.target.value).run()}
                    className="w-8 h-6 border border-gray-300 rounded cursor-pointer"
                    title="Text Color"
                  />
                  <Palette className="w-3 h-3 absolute top-0 right-0 pointer-events-none" />
                </div>

                {/* Highlight Color */}
                <div className="relative">
                  <input
                    type="color"
                    onChange={(e) => editor.chain().focus().toggleHighlight({ color: e.target.value }).run()}
                    className="w-8 h-6 border border-gray-300 rounded cursor-pointer"
                    title="Highlight Color"
                  />
                  <Highlighter className="w-3 h-3 absolute top-0 right-0 pointer-events-none" />
                </div>

                {/* Clear Formatting */}
                <button
                  onClick={() => editor.chain().focus().unsetAllMarks().run()}
                  className="p-1.5 rounded hover:bg-gray-200"
                  title="Clear Formatting"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>

              {/* Second Row - Paragraph Formatting */}
              <div className="flex flex-wrap items-center gap-1 mb-2 pb-2 border-b border-gray-200">
                {/* Headings */}
                <select
                  value={
                    editor.isActive('heading', { level: 1 }) ? 'h1' :
                    editor.isActive('heading', { level: 2 }) ? 'h2' :
                    editor.isActive('heading', { level: 3 }) ? 'h3' :
                    editor.isActive('heading', { level: 4 }) ? 'h4' :
                    editor.isActive('heading', { level: 5 }) ? 'h5' :
                    editor.isActive('heading', { level: 6 }) ? 'h6' : 'p'
                  }
                  onChange={(e) => {
                    const level = e.target.value;
                    if (level === 'p') {
                      editor.chain().focus().setParagraph().run();
                    } else {
                      editor.chain().focus().toggleHeading({ level: parseInt(level.replace('h', '')) }).run();
                    }
                  }}
                  className="px-2 py-1 text-sm border border-gray-300 rounded"
                >
                  <option value="p">Paragraph</option>
                  <option value="h1">Heading 1</option>
                  <option value="h2">Heading 2</option>
                  <option value="h3">Heading 3</option>
                  <option value="h4">Heading 4</option>
                  <option value="h5">Heading 5</option>
                  <option value="h6">Heading 6</option>
                </select>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Text Alignment */}
                <button
                  onClick={() => editor.chain().focus().setTextAlign('left').run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive({ textAlign: 'left' }) ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Align Left"
                >
                  <AlignLeft className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().setTextAlign('center').run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive({ textAlign: 'center' }) ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Align Center"
                >
                  <AlignCenter className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().setTextAlign('right').run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive({ textAlign: 'right' }) ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Align Right"
                >
                  <AlignRight className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive({ textAlign: 'justify' }) ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Justify"
                >
                  <AlignJustify className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Indentation */}
                <button
                  onClick={() => editor.chain().focus().sinkListItem('listItem').run()}
                  disabled={!editor.can().sinkListItem('listItem')}
                  className="p-1.5 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Increase Indent"
                >
                  <Indent className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().liftListItem('listItem').run()}
                  disabled={!editor.can().liftListItem('listItem')}
                  className="p-1.5 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Decrease Indent"
                >
                  <Outdent className="w-4 h-4" />
                </button>
              </div>

              {/* Third Row - Lists, Links, and Advanced Features */}
              <div className="flex flex-wrap items-center gap-1">
                {/* Lists */}
                <button
                  onClick={() => editor.chain().focus().toggleBulletList().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('bulletList') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Bullet List"
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().toggleOrderedList().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('orderedList') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Numbered List"
                >
                  <ListOrdered className="w-4 h-4" />
                </button>

                {/* Task List */}
                <button
                  onClick={() => editor.chain().focus().toggleTaskList().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('taskList') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Task List"
                >
                  <CheckSquare className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Blockquote and Code */}
                <button
                  onClick={() => editor.chain().focus().toggleBlockquote().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('blockquote') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Blockquote"
                >
                  <Quote className="w-4 h-4" />
                </button>
                <button
                  onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('codeBlock') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Code Block"
                >
                  <Code className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Link */}
                <button
                  onClick={() => {
                    const url = window.prompt('Enter URL:');
                    if (url) {
                      editor.chain().focus().setLink({ href: url }).run();
                    }
                  }}
                  className={`p-1.5 rounded hover:bg-gray-200 ${editor.isActive('link') ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Insert Link"
                >
                  <Link className="w-4 h-4" />
                </button>

                {/* Image Upload */}
                <label className="p-1.5 rounded hover:bg-gray-200 cursor-pointer" title="Insert Image">
                  <Image className="w-4 h-4" />
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageUpload}
                    disabled={imageUploading}
                  />
                </label>

                {/* Table */}
                <button
                  onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
                  className="p-1.5 rounded hover:bg-gray-200"
                  title="Insert Table"
                >
                  <Table className="w-4 h-4" />
                </button>

                {/* Table Controls (only show when in table) */}
                {editor.isActive('table') && (
                  <>
                    <button
                      onClick={() => editor.chain().focus().addRowBefore().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs"
                      title="Add Row Before"
                    >
                      +R↑
                    </button>
                    <button
                      onClick={() => editor.chain().focus().addRowAfter().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs"
                      title="Add Row After"
                    >
                      +R↓
                    </button>
                    <button
                      onClick={() => editor.chain().focus().addColumnBefore().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs"
                      title="Add Column Before"
                    >
                      +C←
                    </button>
                    <button
                      onClick={() => editor.chain().focus().addColumnAfter().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs"
                      title="Add Column After"
                    >
                      +C→
                    </button>
                    <button
                      onClick={() => editor.chain().focus().deleteRow().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs text-red-600"
                      title="Delete Row"
                    >
                      -R
                    </button>
                    <button
                      onClick={() => editor.chain().focus().deleteColumn().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs text-red-600"
                      title="Delete Column"
                    >
                      -C
                    </button>
                    <button
                      onClick={() => editor.chain().focus().deleteTable().run()}
                      className="p-1.5 rounded hover:bg-gray-200 text-xs text-red-600"
                      title="Delete Table"
                    >
                      -T
                    </button>
                  </>
                )}

                {/* Horizontal Rule */}
                <button
                  onClick={() => editor.chain().focus().setHorizontalRule().run()}
                  className="p-1.5 rounded hover:bg-gray-200"
                  title="Insert Horizontal Rule"
                >
                  <Minus className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Preview Toggle */}
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className={`p-1.5 rounded hover:bg-gray-200 ${showPreview ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Toggle Preview"
                >
                  <Eye className="w-4 h-4" />
                </button>

                {/* Word Count Toggle */}
                <button
                  onClick={() => setShowWordCount(!showWordCount)}
                  className={`p-1.5 rounded hover:bg-gray-200 ${showWordCount ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Toggle Word Count"
                >
                  <Type className="w-4 h-4" />
                </button>

                {/* Find/Replace */}
                <button
                  onClick={() => setShowFindReplace(!showFindReplace)}
                  className={`p-1.5 rounded hover:bg-gray-200 ${showFindReplace ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Find & Replace (Ctrl+F)"
                >
                  <Search className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Emoji and Special Characters */}
                <button
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  className={`p-1.5 rounded hover:bg-gray-200 ${showEmojiPicker ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Insert Emoji"
                >
                  <Smile className="w-4 h-4" />
                </button>

                <button
                  onClick={() => setShowSpecialChars(!showSpecialChars)}
                  className={`p-1.5 rounded hover:bg-gray-200 ${showSpecialChars ? 'bg-blue-200 text-blue-800' : ''}`}
                  title="Special Characters"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </button>

                <div className="w-px h-6 bg-gray-300 mx-1"></div>

                {/* Export and Print */}
                <button
                  onClick={handleExportHTML}
                  className="p-1.5 rounded hover:bg-gray-200"
                  title="Export as HTML"
                  disabled={!form.content}
                >
                  <Download className="w-4 h-4" />
                </button>

                <button
                  onClick={handlePrint}
                  className="p-1.5 rounded hover:bg-gray-200"
                  title="Print"
                  disabled={!form.content}
                >
                  <Printer className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}

          {/* Find/Replace Panel */}
          {showFindReplace && (
            <div className="border-x border-gray-300 bg-gray-50 p-3">
              <div className="flex items-center gap-2 mb-2">
                <input
                  type="text"
                  placeholder="Find..."
                  value={findText}
                  onChange={(e) => setFindText(e.target.value)}
                  className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                  onKeyDown={(e) => e.key === 'Enter' && handleFind()}
                />
                <button
                  onClick={handleFind}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Find
                </button>
                <button
                  onClick={() => setShowFindReplace(false)}
                  className="p-1 text-gray-500 hover:text-gray-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  placeholder="Replace with..."
                  value={replaceText}
                  onChange={(e) => setReplaceText(e.target.value)}
                  className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded"
                  onKeyDown={(e) => e.key === 'Enter' && handleReplace()}
                />
                <button
                  onClick={handleReplace}
                  className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Replace
                </button>
                <button
                  onClick={handleReplaceAll}
                  className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Replace All
                </button>
              </div>
            </div>
          )}

          {/* Emoji Picker Panel */}
          {showEmojiPicker && (
            <div className="border-x border-gray-300 bg-gray-50 p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">Insert Emoji</h4>
                <button
                  onClick={() => setShowEmojiPicker(false)}
                  className="p-1 text-gray-500 hover:text-gray-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="grid grid-cols-10 gap-1 max-h-32 overflow-y-auto">
                {commonEmojis.map((emoji, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      editor.chain().focus().insertContent(emoji).run();
                      setShowEmojiPicker(false);
                    }}
                    className="p-1 text-lg hover:bg-gray-200 rounded"
                    title={`Insert ${emoji}`}
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Special Characters Panel */}
          {showSpecialChars && (
            <div className="border-x border-gray-300 bg-gray-50 p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium">Special Characters</h4>
                <button
                  onClick={() => setShowSpecialChars(false)}
                  className="p-1 text-gray-500 hover:text-gray-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="grid grid-cols-15 gap-1 max-h-32 overflow-y-auto text-sm">
                {specialChars.slice(0, 50).map((char, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      editor.chain().focus().insertContent(char).run();
                      setShowSpecialChars(false);
                    }}
                    className="p-1 hover:bg-gray-200 rounded text-center"
                    title={`Insert ${char}`}
                  >
                    {char}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Editor Content */}
          <div className="relative">
            {showPreview ? (
              <div className="border border-gray-300 rounded-b-md p-4 min-h-[500px] bg-white prose prose-lg max-w-none">
                <div dangerouslySetInnerHTML={{ __html: form.content }} />
              </div>
            ) : (
              <div className="border border-gray-300 rounded-b-md bg-white">
                <EditorContent editor={editor} />
              </div>
            )}

            {imageUploading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mb-2"></div>
                  <p className="text-gray-600">Uploading image...</p>
                </div>
              </div>
            )}
          </div>

          {/* Word Count and Status */}
          {editor && showWordCount && (
            <div className="flex justify-between items-center mt-2 text-sm text-gray-500">
              <div>
                Words: {editor.storage.characterCount.words()} |
                Characters: {editor.storage.characterCount.characters()} |
                Characters (no spaces): {editor.storage.characterCount.characters({ mode: 'textOnly' })}
              </div>
              {autoSaveEnabled && editingBlog && (
                <div className="text-green-600">
                  Auto-save enabled
                </div>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          <div className="flex gap-2">
            <button
              onClick={resetForm}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={saving}
            >
              Clear
            </button>
            {editingBlog && (
              <button
                onClick={() => resetForm()}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                disabled={saving}
              >
                Cancel Edit
              </button>
            )}
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => handleSave('draft')}
              disabled={saving || !form.title.trim() || !form.content.trim()}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              {saving ? 'Saving...' : 'Save Draft'}
            </button>
            <button
              onClick={() => handleSave('published')}
              disabled={saving || !form.title.trim() || !form.content.trim()}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? 'Publishing...' : 'Publish'}
            </button>
          </div>
        </div>
      </div>

      {/* Blog Posts List */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Published Blog Posts</h3>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            <p className="mt-2 text-gray-600">Loading blog posts...</p>
          </div>
        ) : blogs.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-gray-500">No blog posts created yet.</p>
            <p className="text-sm text-gray-400">Create your first blog post using the editor above.</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {blogs.map((blog) => (
              <div key={blog.id} className="p-6 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      {blog.title}
                    </h4>
                    <div className="text-sm text-gray-500 mb-2">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        blog.status === 'published'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {blog.status}
                      </span>
                      <span className="ml-2">
                        Created: {new Date(blog.created_at).toLocaleDateString()}
                      </span>
                      {blog.updated_at && blog.updated_at !== blog.created_at && (
                        <span className="ml-2">
                          Updated: {new Date(blog.updated_at).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    <div
                      className="text-gray-600 text-sm line-clamp-3"
                      dangerouslySetInnerHTML={{
                        __html: blog.content.length > 200
                          ? blog.content.substring(0, 200) + '...'
                          : blog.content
                      }}
                    />
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => handleEdit(blog)}
                      className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                      title="Edit blog post"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(blog.id)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                      title="Delete blog post"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      </div>
    </div>
  );
};

export default AdminBlogTab;
