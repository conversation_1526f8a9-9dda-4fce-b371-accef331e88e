// AdminAdsImageTab.js
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Upload, Edit, Trash2, X, ChevronLeft, ChevronRight, Info, Play, Pause } from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import './ImageModal.css';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const AdminAdsImageTab = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [form, setForm] = useState({
    file: null,
  });
  const [progress, setProgress] = useState(0);

  // Modal and slider state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAutoSliding, setIsAutoSliding] = useState(true);
  const [showMetadata, setShowMetadata] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  // Refs for cleanup and performance
  const autoSlideInterval = useRef(null);
  const pauseTimeout = useRef(null);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageTransition, setImageTransition] = useState(false);

  useEffect(() => {
    fetchImages();
  }, []);

  // Modal helper functions
  const openModal = (imageIndex) => {
    setCurrentImageIndex(imageIndex);
    setIsModalOpen(true);
    setIsAutoSliding(true);
    setIsPaused(false);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsAutoSliding(false);
    clearInterval(autoSlideInterval.current);
    clearTimeout(pauseTimeout.current);
  };

  const nextImage = useCallback(() => {
    if (images.length > 0) {
      setImageTransition(true);
      setTimeout(() => {
        setCurrentImageIndex((prev) => (prev + 1) % images.length);
        setImageTransition(false);
      }, 150);
    }
  }, [images.length]);

  const prevImage = useCallback(() => {
    if (images.length > 0) {
      setImageTransition(true);
      setTimeout(() => {
        setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
        setImageTransition(false);
      }, 150);
    }
  }, [images.length]);

  const goToImage = (index) => {
    setCurrentImageIndex(index);
    pauseAutoSlide();
  };

  const pauseAutoSlide = () => {
    setIsPaused(true);
    clearTimeout(pauseTimeout.current);
    pauseTimeout.current = setTimeout(() => {
      setIsPaused(false);
    }, 3000); // Resume after 3 seconds of no interaction
  };

  const toggleAutoSlide = () => {
    setIsAutoSliding(!isAutoSliding);
    if (isAutoSliding) {
      clearInterval(autoSlideInterval.current);
    }
  };

  // Auto-slide effect
  useEffect(() => {
    if (isModalOpen && isAutoSliding && !isPaused && images.length > 1) {
      autoSlideInterval.current = setInterval(() => {
        nextImage();
      }, 4000); // 4 seconds interval
    } else {
      clearInterval(autoSlideInterval.current);
    }

    return () => clearInterval(autoSlideInterval.current);
  }, [isModalOpen, isAutoSliding, isPaused, images.length, nextImage]);

  // Keyboard navigation effect
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!isModalOpen) return;

      switch (e.key) {
        case 'Escape':
          closeModal();
          break;
        case 'ArrowLeft':
          prevImage();
          pauseAutoSlide();
          break;
        case 'ArrowRight':
          nextImage();
          pauseAutoSlide();
          break;
        case ' ': // Spacebar
          e.preventDefault();
          toggleAutoSlide();
          break;
        case 'i':
        case 'I':
          setShowMetadata(!showMetadata);
          break;
        default:
          break;
      }
    };

    if (isModalOpen) {
      document.addEventListener('keydown', handleKeyPress);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen, nextImage, prevImage, showMetadata]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearInterval(autoSlideInterval.current);
      clearTimeout(pauseTimeout.current);
      document.body.style.overflow = 'unset';
    };
  }, []);

  const fetchImages = async () => {
    setLoading(true);
    try {
      const res = await axios.get(`${API}/adimages`);
      setImages(res.data);
    } catch {
      toast.error('Failed to fetch images');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, files } = e.target;
    const file = files ? files[0] : null;

    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please select a valid image file (JPG, PNG, GIF, or WebP)');
        e.target.value = '';
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast.error('File size must be less than 10MB');
        e.target.value = '';
        return;
      }
    }

    setForm((prev) => ({
      ...prev,
      [name]: file,
    }));
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (!form.file) {
      toast.error('Please select an image file');
      return;
    }
    setUploading(true);
    setProgress(0);
    const data = new FormData();
    data.append('file', form.file);

    try {
      await axios.post(`${API}/adimages/upload`, data, {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (evt) => {
          setProgress(Math.round((evt.loaded * 100) / evt.total));
        },
      });
      toast.success('Image uploaded successfully');
      setForm({ file: null });
      setProgress(0);
      fetchImages();
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Upload failed';
      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Delete this image?')) return;
    try {
      await axios.delete(`${API}/adimages/${id}`);
      toast.success('Image deleted successfully');
      fetchImages();
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Delete failed';
      toast.error(errorMessage);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
        <Upload className="w-5 h-5" /> Ad Image Management
      </h2>
      <form onSubmit={handleUpload} className="bg-white rounded-lg shadow p-4 mb-6 space-y-4">
        <div>
          <label className="block mb-1 font-medium">Image File (will be saved to <b>/uploads/adimages/</b>)</label>
          <input
            type="file"
            name="file"
            accept=".jpg,.jpeg,.png,.gif,.webp"
            onChange={handleInputChange}
            required
            className="w-full border px-2 py-1 rounded"
          />
          <p className="text-sm text-gray-500 mt-1">
            Supported formats: JPG, JPEG, PNG, GIF, WebP (Max size: 10MB)
          </p>
        </div>
        <button type="submit" disabled={uploading} className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 disabled:opacity-50">
          {uploading ? 'Uploading...' : 'Upload Image'}
        </button>
        {uploading && (
          <div className="w-full bg-gray-200 rounded h-2 mt-2">
            <div className="bg-purple-500 h-2 rounded transition-all duration-300" style={{ width: `${progress}%` }} />
          </div>
        )}
      </form>
      <h3 className="text-lg font-semibold mb-4">Uploaded Images</h3>
      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <p className="mt-2 text-gray-600">Loading images...</p>
        </div>
      ) : images.length === 0 ? (
        <div className="text-center py-8 bg-white rounded-lg shadow">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-gray-500">No images uploaded yet.</p>
          <p className="text-sm text-gray-400">Upload your first ad image using the form above.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {images.map((image) => (
            <div key={image.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="aspect-w-16 aspect-h-9 bg-gray-200 cursor-pointer group relative overflow-hidden">
                <img
                  src={`${BACKEND_URL}/${image.file_path}`}
                  alt={image.original_filename}
                  className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  onClick={() => openModal(images.indexOf(image))}
                  onError={(e) => {
                    e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                  <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-sm font-medium">Click to view</div>
                  </div>
                </div>
              </div>
              <div className="p-4">
                <h4 className="font-medium text-gray-900 truncate" title={image.original_filename}>
                  {image.original_filename}
                </h4>
                <div className="mt-2 space-y-1 text-sm text-gray-500">
                  <p>Size: {(image.file_size / 1024 / 1024).toFixed(2)} MB</p>
                  <p>Type: {image.mime_type}</p>
                  <p>Uploaded: {new Date(image.upload_timestamp).toLocaleDateString()}</p>
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <a
                    href={`${BACKEND_URL}/${image.file_path}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View Full Size
                  </a>
                  <button
                    onClick={() => handleDelete(image.id)}
                    className="text-red-600 hover:text-red-800 p-1 rounded"
                    title="Delete image"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Image Modal */}
      {isModalOpen && images.length > 0 && (
        <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
          {/* Overlay */}
          <div
            className="absolute inset-0 bg-black bg-opacity-90 transition-opacity duration-300 backdrop-blur-sm"
            onClick={closeModal}
          />

          {/* Modal Content */}
          <div className="relative w-full h-full flex flex-col">
            {/* Header */}
            <div className="absolute top-0 left-0 right-0 z-10 flex justify-between items-center p-4 modal-header">
              <div className="flex items-center space-x-2 md:space-x-4 modal-controls">
                {images.length > 1 && (
                  <button
                    onClick={toggleAutoSlide}
                    className="modal-button text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
                    title={isAutoSliding ? "Pause slideshow (Space)" : "Start slideshow (Space)"}
                  >
                    {isAutoSliding ? <Pause className="w-4 h-4 md:w-5 md:h-5" /> : <Play className="w-4 h-4 md:w-5 md:h-5" />}
                  </button>
                )}
                <button
                  onClick={() => setShowMetadata(!showMetadata)}
                  className="modal-button text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
                  title="Toggle image info (I)"
                >
                  <Info className="w-4 h-4 md:w-5 md:h-5" />
                </button>
              </div>
              <button
                onClick={closeModal}
                className="modal-button text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-white"
                title="Close (Esc)"
              >
                <X className="w-5 h-5 md:w-6 md:h-6" />
              </button>
            </div>

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <button
                  onClick={() => { prevImage(); pauseAutoSlide(); }}
                  className="navigation-arrow absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-all duration-200 p-2 md:p-3 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 hover:scale-110 z-10 focus:outline-none focus:ring-2 focus:ring-white"
                  title="Previous image (←)"
                >
                  <ChevronLeft className="w-6 h-6 md:w-8 md:h-8" />
                </button>
                <button
                  onClick={() => { nextImage(); pauseAutoSlide(); }}
                  className="navigation-arrow absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-all duration-200 p-2 md:p-3 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70 hover:scale-110 z-10 focus:outline-none focus:ring-2 focus:ring-white"
                  title="Next image (→)"
                >
                  <ChevronRight className="w-6 h-6 md:w-8 md:h-8" />
                </button>
              </>
            )}

            {/* Main Image */}
            <div className="flex-1 flex items-center justify-center p-4 pt-20 pb-32 modal-content">
              <div className="relative max-w-full max-h-full animate-slideIn">
                <img
                  src={`${BACKEND_URL}/${images[currentImageIndex].file_path}`}
                  alt={images[currentImageIndex].original_filename}
                  className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl image-transition ${
                    imageTransition ? 'image-loading' : ''
                  }`}
                  style={{ maxHeight: 'calc(100vh - 200px)' }}
                  onLoad={() => setImageLoading(false)}
                  onLoadStart={() => setImageLoading(true)}
                />

                {/* Metadata Overlay */}
                {showMetadata && (
                  <div className="metadata-overlay absolute bottom-4 left-2 right-2 md:left-4 md:right-4 bg-black bg-opacity-80 backdrop-blur-sm text-white p-3 md:p-4 rounded-lg transition-all duration-300 animate-slideIn">
                    <h3 className="font-semibold text-base md:text-lg mb-2 truncate" title={images[currentImageIndex].original_filename}>
                      {images[currentImageIndex].original_filename}
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 text-xs md:text-sm">
                      <div className="flex flex-col sm:flex-row sm:items-center">
                        <span className="text-gray-300 font-medium">Size:</span>
                        <span className="sm:ml-1">{(images[currentImageIndex].file_size / 1024 / 1024).toFixed(2)} MB</span>
                      </div>
                      <div className="flex flex-col sm:flex-row sm:items-center">
                        <span className="text-gray-300 font-medium">Type:</span>
                        <span className="sm:ml-1">{images[currentImageIndex].mime_type}</span>
                      </div>
                      <div className="flex flex-col sm:flex-row sm:items-center">
                        <span className="text-gray-300 font-medium">Uploaded:</span>
                        <span className="sm:ml-1">{new Date(images[currentImageIndex].upload_timestamp).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Bottom Controls */}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black to-transparent">
              {/* Dot Indicators */}
              {images.length > 1 && (
                <div className="flex justify-center mb-4">
                  <div className="flex space-x-2">
                    {images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => goToImage(index)}
                        className={`dot-indicator dot-button w-2 h-2 md:w-3 md:h-3 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white ${
                          index === currentImageIndex
                            ? 'bg-white scale-125 shadow-lg'
                            : 'bg-white bg-opacity-50 hover:bg-opacity-75 hover:scale-110'
                        }`}
                        title={`Go to image ${index + 1}`}
                        aria-label={`Go to image ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Thumbnail Strip */}
              {images.length > 1 && (
                <div className="flex justify-center">
                  <div className="thumbnail-strip flex space-x-2 overflow-x-auto max-w-full pb-2 px-4">
                    {images.map((image, index) => (
                      <button
                        key={image.id}
                        onClick={() => goToImage(index)}
                        className={`thumbnail-item thumbnail-button flex-shrink-0 w-12 h-9 md:w-16 md:h-12 rounded overflow-hidden transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white ${
                          index === currentImageIndex
                            ? 'ring-2 ring-white scale-110 shadow-lg'
                            : 'opacity-70 hover:opacity-100 hover:scale-105'
                        }`}
                        title={image.original_filename}
                        aria-label={`View ${image.original_filename}`}
                      >
                        <img
                          src={`${BACKEND_URL}/${image.file_path}`}
                          alt={image.original_filename}
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminAdsImageTab;