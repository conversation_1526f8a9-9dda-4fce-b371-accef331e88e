# PowerShell script to test image upload functionality

# Configuration
$BACKEND_URL = "http://localhost:8001"
$API_BASE = "$BACKEND_URL/api"

Write-Host "🧪 Testing Image Upload Functionality" -ForegroundColor Cyan
Write-Host "=" * 50

# Test 1: Health check
Write-Host "1. Testing backend health..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "$API_BASE/health"
    Write-Host "✅ Backend is running" -ForegroundColor Green
    Write-Host "   Status: $($health.status)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Backend health check failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Admin login
Write-Host "`n2. Testing admin login..." -ForegroundColor Yellow
try {
    $loginBody = @{
        email = "<EMAIL>"
        password = "admin123"
    } | ConvertTo-Json
    
    $loginResponse = Invoke-RestMethod -Uri "$API_BASE/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.access_token
    Write-Host "✅ Admin login successful" -ForegroundColor Green
    Write-Host "   Token: $($token.Substring(0, 20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Admin login failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try to initialize admin
    Write-Host "`n   Trying to initialize admin user..." -ForegroundColor Yellow
    try {
        $initResponse = Invoke-RestMethod -Uri "$API_BASE/admin/init" -Method POST
        Write-Host "✅ Admin user initialized" -ForegroundColor Green
        
        # Retry login
        $loginResponse = Invoke-RestMethod -Uri "$API_BASE/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
        $token = $loginResponse.access_token
        Write-Host "✅ Admin login successful after initialization" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to initialize admin user" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Test 3: Create a test image
Write-Host "`n3. Creating test image..." -ForegroundColor Yellow
$testImagePath = "test_upload.png"

# Create a simple 1x1 PNG image (base64 encoded)
$pngBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
$pngBytes = [System.Convert]::FromBase64String($pngBase64)
[System.IO.File]::WriteAllBytes($testImagePath, $pngBytes)
Write-Host "✅ Test image created: $testImagePath" -ForegroundColor Green

# Test 4: Upload image
Write-Host "`n4. Testing image upload..." -ForegroundColor Yellow
try {
    $headers = @{
        Authorization = "Bearer $token"
    }
    
    # Create multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    $fileBytes = [System.IO.File]::ReadAllBytes($testImagePath)
    $fileContent = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)
    
    $bodyLines = (
        "--$boundary",
        "Content-Disposition: form-data; name=`"file`"; filename=`"test_upload.png`"",
        "Content-Type: image/png$LF",
        $fileContent,
        "--$boundary--$LF"
    ) -join $LF
    
    $uploadResponse = Invoke-RestMethod -Uri "$API_BASE/adimages/upload" -Method POST -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary" -Headers $headers
    
    Write-Host "✅ Image upload successful" -ForegroundColor Green
    Write-Host "   Image ID: $($uploadResponse.id)" -ForegroundColor Gray
    Write-Host "   Original filename: $($uploadResponse.original_filename)" -ForegroundColor Gray
    Write-Host "   File path: $($uploadResponse.file_path)" -ForegroundColor Gray
    Write-Host "   File size: $($uploadResponse.file_size) bytes" -ForegroundColor Gray
    
    $uploadedImageId = $uploadResponse.id
} catch {
    Write-Host "❌ Image upload failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "   Response: $responseBody" -ForegroundColor Red
    }
}

# Test 5: List images
Write-Host "`n5. Testing image list..." -ForegroundColor Yellow
try {
    $headers = @{
        Authorization = "Bearer $token"
    }
    
    $images = Invoke-RestMethod -Uri "$API_BASE/adimages" -Method GET -Headers $headers
    Write-Host "✅ Image list retrieved successfully" -ForegroundColor Green
    Write-Host "   Total images: $($images.Count)" -ForegroundColor Gray
    
    foreach ($img in $images) {
        Write-Host "   - $($img.original_filename) ($($img.file_size) bytes, uploaded: $($img.upload_timestamp))" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Image list failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Check if image file exists
Write-Host "`n6. Checking uploaded file..." -ForegroundColor Yellow
if ($uploadedImageId) {
    try {
        $imageUrl = "$BACKEND_URL/$($uploadResponse.file_path)"
        $imageCheck = Invoke-WebRequest -Uri $imageUrl -UseBasicParsing
        Write-Host "✅ Uploaded image is accessible" -ForegroundColor Green
        Write-Host "   URL: $imageUrl" -ForegroundColor Gray
        Write-Host "   Content-Type: $($imageCheck.Headers.'Content-Type')" -ForegroundColor Gray
    } catch {
        Write-Host "❌ Uploaded image is not accessible" -ForegroundColor Red
        Write-Host "   URL: $imageUrl" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Cleanup
Write-Host "`n7. Cleaning up..." -ForegroundColor Yellow
if (Test-Path $testImagePath) {
    Remove-Item $testImagePath
    Write-Host "✅ Test image file removed" -ForegroundColor Green
}

Write-Host "`n" + "=" * 50
Write-Host "🎉 Testing completed!" -ForegroundColor Cyan
