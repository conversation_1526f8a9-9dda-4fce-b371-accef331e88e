/* Enhanced Tiptap editor styles for Microsoft Word-level functionality */
.ProseMirror {
  min-height: 500px;
  outline: none;
  font-size: 1rem;
  line-height: 1.6;
  padding: 1rem;
  color: #222;
  background: #fff;
  font-family: 'Arial', sans-serif;
}

.ProseMirror:focus {
  outline: 2px solid #a78bfa;
}

/* Focus styling */
.ProseMirror.has-focus {
  outline: 2px solid #a78bfa;
}

/* Typography */
.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-top: 1.75rem;
  margin-bottom: 0.875rem;
  line-height: 1.3;
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.ProseMirror h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.625rem;
  line-height: 1.4;
}

.ProseMirror h5 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1.125rem;
  margin-bottom: 0.5625rem;
  line-height: 1.4;
}

.ProseMirror h6 {
  font-size: 1rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.ProseMirror p {
  margin: 0.75rem 0;
  line-height: 1.6;
}

/* Text formatting */
.ProseMirror strong {
  font-weight: bold;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror u {
  text-decoration: underline;
}

.ProseMirror s {
  text-decoration: line-through;
}

.ProseMirror sub {
  vertical-align: sub;
  font-size: smaller;
}

.ProseMirror sup {
  vertical-align: super;
  font-size: smaller;
}

/* Lists */
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 2rem;
  margin: 1rem 0;
}

.ProseMirror ul ul,
.ProseMirror ol ol,
.ProseMirror ul ol,
.ProseMirror ol ul {
  margin: 0.5rem 0;
}

.ProseMirror li {
  margin: 0.25rem 0;
  line-height: 1.6;
}

.ProseMirror li p {
  margin: 0.25rem 0;
}

/* Blockquotes */
.ProseMirror blockquote {
  border-left: 4px solid #a78bfa;
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  color: #555;
  font-style: italic;
  background: #f9f9ff;
  border-radius: 0 4px 4px 0;
}

.ProseMirror blockquote p {
  margin: 0.5rem 0;
}

/* Code blocks */
.ProseMirror pre {
  background: #f4f4f4;
  color: #333;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', 'Fira Mono', monospace;
  font-size: 0.9em;
  overflow-x: auto;
  margin: 1rem 0;
  border: 1px solid #e5e5e5;
}

.ProseMirror code {
  background: #f1f1f1;
  color: #d63384;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', 'Fira Mono', monospace;
  font-size: 0.9em;
}

/* Links */
.ProseMirror a {
  color: #2563eb;
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ProseMirror img.ProseMirror-selectednode {
  outline: 2px solid #a78bfa;
  outline-offset: 2px;
}

/* Tables */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1rem 0;
  overflow: hidden;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

.ProseMirror table td,
.ProseMirror table th {
  min-width: 1em;
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
  background: #fff;
}

.ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background: #f9fafb;
}

.ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.ProseMirror table p {
  margin: 0;
}

/* Horizontal Rule */
.ProseMirror hr {
  border: none;
  border-top: 2px solid #d1d5db;
  margin: 2rem 0;
}

/* Text alignment */
.ProseMirror[style*="text-align: left"] {
  text-align: left;
}

.ProseMirror[style*="text-align: center"] {
  text-align: center;
}

.ProseMirror[style*="text-align: right"] {
  text-align: right;
}

.ProseMirror[style*="text-align: justify"] {
  text-align: justify;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Selection */
.ProseMirror ::selection {
  background: #b3d4fc;
}

.ProseMirror ::-moz-selection {
  background: #b3d4fc;
}

/* Gapcursor */
.ProseMirror .ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
}

.ProseMirror .ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror .ProseMirror-gapcursor.ProseMirror-gapcursor-left:after {
  left: -10px;
}

.ProseMirror .ProseMirror-gapcursor.ProseMirror-gapcursor-right:after {
  right: -10px;
}

/* Highlight colors */
.ProseMirror mark {
  background-color: #fff3cd;
  color: inherit;
  padding: 0.1em 0.2em;
  border-radius: 2px;
}

/* Custom font families */
.ProseMirror [style*="font-family"] {
  font-family: inherit;
}

/* Responsive design */
@media (max-width: 768px) {
  .ProseMirror {
    padding: 0.75rem;
    font-size: 0.95rem;
  }

  .ProseMirror h1 {
    font-size: 1.875rem;
  }

  .ProseMirror h2 {
    font-size: 1.5rem;
  }

  .ProseMirror h3 {
    font-size: 1.25rem;
  }

  .ProseMirror table {
    font-size: 0.875rem;
  }

  .ProseMirror table td,
  .ProseMirror table th {
    padding: 6px 8px;
  }
}

/* Task Lists */
.ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.5rem 0;
}

.ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
  margin: 0;
}

.ProseMirror ul[data-type="taskList"] li[data-checked="true"] > div {
  text-decoration: line-through;
  color: #6b7280;
}

/* Dropcursor */
.ProseMirror .ProseMirror-dropcursor {
  position: relative;
  pointer-events: none;
}

.ProseMirror .ProseMirror-dropcursor:after {
  content: '';
  display: block;
  position: absolute;
  left: -2px;
  right: -2px;
  top: 0;
  bottom: 0;
  border-left: 2px solid #a78bfa;
  border-radius: 1px;
}

/* Enhanced table styles */
.ProseMirror table .selectedCell {
  background: rgba(167, 139, 250, 0.2);
  position: relative;
}

.ProseMirror table .selectedCell::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(167, 139, 250, 0.2);
  pointer-events: none;
}

/* Font size support */
.ProseMirror [style*="font-size"] {
  font-size: inherit !important;
}

/* Improved focus styles */
.ProseMirror:focus-within {
  outline: 2px solid #a78bfa;
  outline-offset: -2px;
}

/* Better list nesting */
.ProseMirror ul ul,
.ProseMirror ol ol,
.ProseMirror ul ol,
.ProseMirror ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

/* Print styles */
@media print {
  .ProseMirror {
    background: white;
    color: black;
    font-size: 12pt;
    line-height: 1.4;
  }

  .ProseMirror h1,
  .ProseMirror h2,
  .ProseMirror h3,
  .ProseMirror h4,
  .ProseMirror h5,
  .ProseMirror h6 {
    page-break-after: avoid;
  }

  .ProseMirror blockquote,
  .ProseMirror pre,
  .ProseMirror table {
    page-break-inside: avoid;
  }

  .ProseMirror ul[data-type="taskList"] input[type="checkbox"] {
    -webkit-appearance: checkbox;
    appearance: checkbox;
  }
}