<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            try {
                console.log('Testing API...');
                resultDiv.innerHTML = 'Testing...';

                const response = await fetch('http://localhost:8001/api/blogs/published');
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('API Response:', data);
                console.log('Number of blogs:', data.length);

                resultDiv.innerHTML = '<h3>Success!</h3><p>Found ' + data.length + ' blogs</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('API Error:', error);
                resultDiv.innerHTML = 'Error: ' + error.message;
            }
        }

        // Auto-test on page load
        window.onload = testAPI;
    </script>
</body>
</html>
