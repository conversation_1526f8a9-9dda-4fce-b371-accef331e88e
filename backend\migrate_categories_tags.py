#!/usr/bin/env python3
"""
Migration script to create categories and tags collections from existing blog posts
"""

import asyncio
import sys
import os
from datetime import datetime
import uuid

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from motor.motor_asyncio import AsyncIOMotorClient

# MongoDB connection - use the same connection as in server.py
MONGODB_URL = "************************************************************"
DATABASE_NAME = "vibrantyoga"

def generate_slug(title: str) -> str:
    """Generate a URL-friendly slug from a title"""
    import re
    # Convert to lowercase and replace spaces with hyphens
    slug = title.lower().strip()
    # Remove special characters and replace with hyphens
    slug = re.sub(r'[^\w\s-]', '', slug)
    # Replace multiple spaces/hyphens with single hyphen
    slug = re.sub(r'[-\s]+', '-', slug)
    # Remove leading/trailing hyphens
    slug = slug.strip('-')
    return slug[:100]  # Limit length

async def migrate_categories_and_tags():
    """Migrate existing blog categories and tags to separate collections"""
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    
    try:
        print("Starting migration of categories and tags...")
        
        # Get all blog posts
        blogs_cursor = db.blogs.find({})
        blogs = await blogs_cursor.to_list(1000)
        
        print(f"Found {len(blogs)} blog posts to process")
        
        # Collect unique categories and tags
        categories_set = set()
        tags_set = set()
        
        for blog in blogs:
            if blog.get('category') and blog['category'].strip():
                categories_set.add(blog['category'].strip())
            
            if blog.get('tags') and isinstance(blog['tags'], list):
                for tag in blog['tags']:
                    if tag and tag.strip():
                        tags_set.add(tag.strip())
        
        print(f"Found {len(categories_set)} unique categories and {len(tags_set)} unique tags")
        
        # Create categories collection
        categories_to_insert = []
        category_name_to_id = {}
        
        for category_name in categories_set:
            category_id = str(uuid.uuid4())
            slug = generate_slug(category_name)
            
            # Count posts in this category
            post_count = await db.blogs.count_documents({"category": category_name})
            
            category_doc = {
                "id": category_id,
                "name": category_name,
                "description": None,
                "slug": slug,
                "color": None,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "post_count": post_count,
                "is_active": True
            }
            
            categories_to_insert.append(category_doc)
            category_name_to_id[category_name] = category_id
        
        # Insert categories
        if categories_to_insert:
            await db.categories.delete_many({})  # Clear existing categories
            await db.categories.insert_many(categories_to_insert)
            print(f"Inserted {len(categories_to_insert)} categories")
        
        # Create tags collection
        tags_to_insert = []
        tag_name_to_id = {}
        
        for tag_name in tags_set:
            tag_id = str(uuid.uuid4())
            slug = generate_slug(tag_name)
            
            # Count posts with this tag
            post_count = await db.blogs.count_documents({"tags": tag_name})
            
            tag_doc = {
                "id": tag_id,
                "name": tag_name,
                "slug": slug,
                "description": None,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "post_count": post_count,
                "is_active": True
            }
            
            tags_to_insert.append(tag_doc)
            tag_name_to_id[tag_name] = tag_id
        
        # Insert tags
        if tags_to_insert:
            await db.tags.delete_many({})  # Clear existing tags
            await db.tags.insert_many(tags_to_insert)
            print(f"Inserted {len(tags_to_insert)} tags")
        
        # Update blog posts with category_id and tag_ids
        updated_blogs = 0
        for blog in blogs:
            update_data = {}
            
            # Add category_id if category exists
            if blog.get('category') and blog['category'].strip():
                category_name = blog['category'].strip()
                if category_name in category_name_to_id:
                    update_data['category_id'] = category_name_to_id[category_name]
            
            # Add tag_ids if tags exist
            if blog.get('tags') and isinstance(blog['tags'], list):
                tag_ids = []
                for tag_name in blog['tags']:
                    if tag_name and tag_name.strip() and tag_name.strip() in tag_name_to_id:
                        tag_ids.append(tag_name_to_id[tag_name.strip()])
                if tag_ids:
                    update_data['tag_ids'] = tag_ids
            
            # Update blog post if there's data to update
            if update_data:
                update_data['updated_at'] = datetime.utcnow()
                await db.blogs.update_one(
                    {"id": blog["id"]}, 
                    {"$set": update_data}
                )
                updated_blogs += 1
        
        print(f"Updated {updated_blogs} blog posts with category_id and tag_ids")
        print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        raise
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(migrate_categories_and_tags())
