import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Calendar, Clock, ArrowLeft, Share2, Heart, MessageCircle, User } from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import '../blog-content.css';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const BlogPost = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [relatedBlogs, setRelatedBlogs] = useState([]);

  useEffect(() => {
    if (id) {
      fetchBlogPost();
      fetchRelatedBlogs();
    }
  }, [id]);

  const fetchBlogPost = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${API}/blogs/${id}`);
      setBlog(response.data);
      
      // Update page title
      document.title = `${response.data.title} - Vibrant Yoga Blog`;
    } catch (error) {
      console.error('Error fetching blog post:', error);
      if (error.response?.status === 404) {
        toast.error('Blog post not found');
        navigate('/blog');
      } else {
        toast.error('Failed to load blog post');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedBlogs = async () => {
    try {
      const response = await axios.get(`${API}/blogs/published`);
      // Filter out current blog and limit to 3 related posts
      const related = response.data.filter(b => b.id !== id).slice(0, 3);
      setRelatedBlogs(related);
    } catch (error) {
      console.error('Error fetching related blogs:', error);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReadingTime = (content) => {
    const textContent = content.replace(/<[^>]*>/g, '');
    const wordsPerMinute = 200;
    const wordCount = textContent.split(/\s+/).length;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return readingTime;
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: blog.title,
          text: `Check out this article: ${blog.title}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const getExcerpt = (content, maxLength = 150) => {
    const textContent = content.replace(/<[^>]*>/g, '');
    if (textContent.length <= maxLength) return textContent;
    return textContent.substring(0, maxLength).trim() + '...';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
            <p className="text-gray-600">Loading blog post...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Blog Post Not Found</h1>
            <p className="text-gray-600 mb-8">The blog post you're looking for doesn't exist.</p>
            <Link
              to="/blog"
              className="inline-flex items-center gap-2 bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
      {/* Navigation */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <Link
            to="/blog"
            className="inline-flex items-center gap-2 text-purple-600 hover:text-purple-700 font-medium"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Blog
          </Link>
        </div>
      </div>

      {/* Article Header */}
      <div className="bg-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-6 text-sm text-gray-500 mb-6">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(blog.created_at)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{getReadingTime(blog.content)} min read</span>
                </div>
                <button
                  onClick={handleShare}
                  className="flex items-center gap-1 text-purple-600 hover:text-purple-700 transition-colors"
                >
                  <Share2 className="w-4 h-4" />
                  <span>Share</span>
                </button>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                {blog.title}
              </h1>
              
              {blog.updated_at && blog.updated_at !== blog.created_at && (
                <p className="text-sm text-gray-500">
                  Last updated: {formatDate(blog.updated_at)}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <article className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-8 md:p-12">
              {/* Blog Content */}
              <div
                className="blog-content"
                dangerouslySetInnerHTML={{ __html: blog.content }}
              />
              
              {/* Article Footer */}
              <div className="mt-12 pt-8 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    Published on {formatDate(blog.created_at)}
                  </div>
                  <button
                    onClick={handleShare}
                    className="flex items-center gap-2 text-purple-600 hover:text-purple-700 font-medium"
                  >
                    <Share2 className="w-4 h-4" />
                    Share this article
                  </button>
                </div>
              </div>
            </div>
          </article>
        </div>
      </div>

      {/* Related Articles */}
      {relatedBlogs.length > 0 && (
        <div className="bg-white">
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-6xl mx-auto">
              <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Related Articles
              </h3>
              
              <div className="grid md:grid-cols-3 gap-8">
                {relatedBlogs.map((relatedBlog) => (
                  <article key={relatedBlog.id} className="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="p-6">
                      <div className="text-sm text-gray-500 mb-3">
                        {formatDate(relatedBlog.created_at)}
                      </div>
                      
                      <h4 className="text-xl font-semibold text-gray-900 mb-3 leading-tight">
                        <Link 
                          to={`/blog/${relatedBlog.id}`}
                          className="hover:text-purple-600 transition-colors"
                        >
                          {relatedBlog.title}
                        </Link>
                      </h4>
                      
                      <p className="text-gray-600 mb-4">
                        {getExcerpt(relatedBlog.content)}
                      </p>
                      
                      <Link
                        to={`/blog/${relatedBlog.id}`}
                        className="text-purple-600 hover:text-purple-700 font-medium"
                      >
                        Read More →
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-2xl mx-auto text-center text-white">
            <h3 className="text-3xl font-bold mb-4">
              Ready to Start Your Yoga Journey?
            </h3>
            <p className="text-lg mb-8 opacity-90">
              Join our community and discover the transformative power of yoga with expert guidance and support.
            </p>
            <Link
              to="/events"
              className="inline-block bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Explore Our Classes
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPost;
