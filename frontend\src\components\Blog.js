import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, ArrowRight, User, Tag, TrendingUp, Hash } from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

console.log('BACKEND_URL:', BACKEND_URL);
console.log('API:', API);

const Blog = () => {
  const [blogs, setBlogs] = useState([]);
  const [categories, setCategories] = useState([]);
  const [trendingTopics, setTrendingTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      console.log('Fetching blog data from:', API);
      const [blogsRes, categoriesRes, trendingRes] = await Promise.all([
        axios.get(`${API}/blogs/published`),
        axios.get(`${API}/blogs/categories`),
        axios.get(`${API}/blogs/trending`)
      ]);

      console.log('Blogs response:', blogsRes.data);
      console.log('Categories response:', categoriesRes.data);
      console.log('Trending response:', trendingRes.data);

      setBlogs(blogsRes.data);
      setCategories(categoriesRes.data);
      setTrendingTopics(trendingRes.data);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load blog data');
    } finally {
      setLoading(false);
    }
  };

  const fetchBlogsByCategory = async (category) => {
    setLoading(true);
    try {
      const response = await axios.get(`${API}/blogs/by-category/${encodeURIComponent(category)}`);
      setBlogs(response.data);
      setSelectedCategory(category);
    } catch (error) {
      console.error('Error fetching blogs by category:', error);
      toast.error('Failed to load category blogs');
    } finally {
      setLoading(false);
    }
  };

  const resetToAllBlogs = async () => {
    setSelectedCategory(null);
    setLoading(true);
    try {
      const response = await axios.get(`${API}/blogs/published`);
      setBlogs(response.data);
    } catch (error) {
      console.error('Error fetching all blogs:', error);
      toast.error('Failed to load blogs');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getExcerpt = (blog) => {
    // Use the excerpt from backend if available, otherwise generate from content
    if (blog.excerpt) return blog.excerpt;
    const textContent = blog.content.replace(/<[^>]*>/g, '');
    if (textContent.length <= 200) return textContent;
    return textContent.substring(0, 200).trim() + '...';
  };

  const getReadingTime = (blog) => {
    // Use reading time from backend if available, otherwise calculate
    if (blog.reading_time) return blog.reading_time;
    const textContent = blog.content.replace(/<[^>]*>/g, '');
    const wordsPerMinute = 200;
    const wordCount = textContent.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Artificial Intelligence': 'bg-purple-100 text-purple-800',
      'Machine Learning': 'bg-blue-100 text-blue-800',
      'Cloud Computing': 'bg-green-100 text-green-800',
      'Cybersecurity': 'bg-red-100 text-red-800',
      'Web Development': 'bg-yellow-100 text-yellow-800',
      'Mobile Development': 'bg-indigo-100 text-indigo-800',
      'DevOps': 'bg-gray-100 text-gray-800',
      'Data Analytics': 'bg-pink-100 text-pink-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  console.log('Rendering Blog component, loading:', loading);
  console.log('Blogs:', blogs);
  console.log('Categories:', categories);
  console.log('Trending topics:', trendingTopics);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
            <p className="text-gray-600">Loading blog posts...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Blog</h1>
              <p className="text-gray-600 mt-2">
                {selectedCategory ? `Category: ${selectedCategory}` : 'Latest insights and articles'}
              </p>
            </div>
            {selectedCategory && (
              <button
                onClick={resetToAllBlogs}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                View All Posts
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Section with Sidebar */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Categories</h2>
              <div className="space-y-3">
                {categories.map((category) => (
                  <div
                    key={category.name}
                    className="flex items-center justify-between cursor-pointer hover:text-purple-600 transition-colors"
                    onClick={() => fetchBlogsByCategory(category.name)}
                  >
                    <div className="flex items-center">
                      <Tag className="w-4 h-4 mr-2 text-gray-500" />
                      <span className={selectedCategory === category.name ? "font-medium text-purple-600" : ""}>
                        {category.name}
                      </span>
                    </div>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                      {category.count}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Trending Topics</h2>
              <div className="space-y-3">
                {trendingTopics.map((topic) => (
                  <div key={topic.name} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <TrendingUp className="w-4 h-4 mr-2 text-gray-500" />
                      <span>{topic.name}</span>
                      {topic.is_new && (
                        <span className="ml-2 text-xs font-medium bg-pink-100 text-pink-800 px-1.5 py-0.5 rounded-full">
                          NEW
                        </span>
                      )}
                    </div>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                      {topic.count} posts
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            {(console.log('Checking blogs.length:', blogs.length), blogs.length === 0) ? (
              <div className="bg-white rounded-lg shadow p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Blog Posts Yet</h3>
                <p className="text-gray-600">
                  We're working on creating amazing content for you. Check back soon!
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {blogs.map((blog, index) => (
                  <article
                    key={blog.id}
                    className="bg-white rounded-lg shadow overflow-hidden hover:shadow-lg transition-shadow duration-300 flex flex-col h-full"
                  >
                    {/* Featured Image */}
                    <div className="relative">
                      {blog.featured_image ? (
                        <img
                          src={blog.featured_image}
                          alt={blog.title}
                          className="w-full h-48 object-cover"
                        />
                      ) : (
                        <div className="w-full h-48 bg-gradient-to-r from-purple-100 to-pink-100 flex items-center justify-center">
                          <svg className="w-12 h-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}

                      {/* Category Badge */}
                      {blog.category && (
                        <span className={`absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(blog.category)}`}>
                          {blog.category}
                        </span>
                      )}
                    </div>

                    {/* Content */}
                    <div className="p-5 flex-grow flex flex-col">
                      {/* Date and Reading Time */}
                      <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3.5 h-3.5" />
                          <span>{formatDate(blog.created_at)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3.5 h-3.5" />
                          <span>{getReadingTime(blog)} min read</span>
                        </div>
                      </div>

                      {/* Title */}
                      <h2 className="text-xl font-bold text-gray-900 mb-3 leading-tight hover:text-purple-600 transition-colors">
                        <Link to={`/blog/${blog.id}`}>
                          {blog.title}
                        </Link>
                      </h2>

                      {/* Excerpt */}
                      <p className="text-gray-600 mb-4 text-sm leading-relaxed flex-grow">
                        {getExcerpt(blog)}
                      </p>

                      {/* Footer */}
                      <div className="mt-auto flex items-center justify-between pt-4 border-t border-gray-100">
                        {/* Author */}
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                            <User className="w-4 h-4 text-gray-500" />
                          </div>
                          <span className="text-sm text-gray-700">
                            {blog.author_name || 'Admin'}
                          </span>
                        </div>

                        {/* Read More Link */}
                        <Link
                          to={`/blog/${blog.id}`}
                          className="text-sm text-purple-600 hover:text-purple-700 font-medium flex items-center"
                        >
                          Read More
                          <ArrowRight className="w-3.5 h-3.5 ml-1" />
                        </Link>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog;
