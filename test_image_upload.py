#!/usr/bin/env python3
"""
Test script for the image upload functionality
"""
import requests
import json
import os
from pathlib import Path

# Configuration
BACKEND_URL = "http://localhost:8001"
API_BASE = f"{BACKEND_URL}/api"

def test_health_check():
    """Test if the backend is running"""
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure it's running on port 8001")
        return False

def test_admin_login():
    """Test admin login to get authentication token"""
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        response = requests.post(f"{API_BASE}/auth/login", json=login_data)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print("✅ Admin login successful")
            return token
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Admin login error: {e}")
        return None

def test_image_upload(token):
    """Test image upload functionality"""
    if not token:
        print("❌ No authentication token available")
        return False
    
    # Create a simple test image file
    test_image_path = "test_image.png"
    
    # Create a simple PNG image (1x1 pixel)
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    with open(test_image_path, 'wb') as f:
        f.write(png_data)
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        files = {"file": ("test_image.png", open(test_image_path, "rb"), "image/png")}
        
        response = requests.post(f"{API_BASE}/adimages/upload", headers=headers, files=files)
        
        if response.status_code == 201:
            print("✅ Image upload successful")
            image_data = response.json()
            print(f"   Image ID: {image_data.get('id')}")
            print(f"   Original filename: {image_data.get('original_filename')}")
            print(f"   File path: {image_data.get('file_path')}")
            return image_data.get('id')
        else:
            print(f"❌ Image upload failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Image upload error: {e}")
        return None
    finally:
        # Clean up test file
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

def test_image_list(token):
    """Test image list functionality"""
    if not token:
        print("❌ No authentication token available")
        return False
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{API_BASE}/adimages", headers=headers)
        
        if response.status_code == 200:
            images = response.json()
            print(f"✅ Image list retrieved successfully ({len(images)} images)")
            for img in images:
                print(f"   - {img.get('original_filename')} ({img.get('file_size')} bytes)")
            return True
        else:
            print(f"❌ Image list failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Image list error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Image Upload Functionality")
    print("=" * 50)
    
    # Test 1: Health check
    if not test_health_check():
        return
    
    # Test 2: Admin login
    token = test_admin_login()
    if not token:
        print("\n💡 Make sure to initialize admin user first:")
        print("   POST http://localhost:8001/api/admin/init")
        return
    
    # Test 3: Image upload
    image_id = test_image_upload(token)
    
    # Test 4: Image list
    test_image_list(token)
    
    print("\n" + "=" * 50)
    print("🎉 Testing completed!")

if __name__ == "__main__":
    main()
