/* Image Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.4s ease-out;
}

.animate-slideLeft {
  animation: slideLeft 0.5s ease-out;
}

.animate-slideRight {
  animation: slideRight 0.5s ease-out;
}

/* Custom scrollbar for thumbnail strip */
.thumbnail-strip::-webkit-scrollbar {
  height: 4px;
}

.thumbnail-strip::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.thumbnail-strip::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.thumbnail-strip::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Smooth transitions for image changes */
.image-transition {
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

/* Loading state */
.image-loading {
  opacity: 0.7;
  transform: scale(0.98);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-content {
    padding: 1rem;
  }
  
  .thumbnail-strip {
    max-width: calc(100vw - 2rem);
  }
  
  .navigation-arrow {
    padding: 0.5rem;
  }
  
  .metadata-overlay {
    font-size: 0.875rem;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 0.75rem;
  }
  
  .modal-controls {
    gap: 0.5rem;
  }
  
  .thumbnail-item {
    width: 3rem;
    height: 2.25rem;
  }
  
  .dot-indicator {
    width: 0.625rem;
    height: 0.625rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal-overlay {
    background-color: rgba(0, 0, 0, 0.95);
  }
  
  .modal-button {
    background-color: rgba(255, 255, 255, 0.9);
    color: black;
  }
  
  .modal-button:hover {
    background-color: white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeIn,
  .animate-slideIn,
  .animate-slideLeft,
  .animate-slideRight {
    animation: none;
  }
  
  .image-transition {
    transition: none;
  }
}

/* Focus styles for accessibility */
.modal-button:focus {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
}

.thumbnail-button:focus {
  outline: 2px solid #60a5fa;
  outline-offset: 1px;
}

.dot-button:focus {
  outline: 2px solid #60a5fa;
  outline-offset: 1px;
}
