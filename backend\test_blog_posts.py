#!/usr/bin/env python3
"""
Test script to create sample blog posts with the new structure
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
import random

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from motor.motor_asyncio import AsyncIOMotorClient
import uuid

# MongoDB connection - use the same connection as in server.py
MONGODB_URL = "************************************************************"
DATABASE_NAME = "vibrantyoga"

# Sample blog posts data
SAMPLE_POSTS = [
    {
        "title": "Getting Started with Artificial Intelligence",
        "content": "<h2>Introduction to AI</h2><p>Artificial Intelligence is transforming the way we work and live. In this comprehensive guide, we'll explore the fundamentals of AI and how you can get started with machine learning projects.</p><h3>What is AI?</h3><p>AI refers to the simulation of human intelligence in machines that are programmed to think and learn like humans. It encompasses various subfields including machine learning, natural language processing, and computer vision.</p><h3>Key Applications</h3><ul><li>Image recognition and computer vision</li><li>Natural language processing</li><li>Predictive analytics</li><li>Autonomous systems</li></ul><p>Whether you're a beginner or looking to advance your skills, understanding AI fundamentals is crucial in today's technology landscape.</p>",
        "category": "Artificial Intelligence",
        "tags": ["AI", "Machine Learning", "Technology", "Beginner"],
        "author_name": "Dr. Sarah Chen",
        "featured_image": "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop",
        "status": "published"
    },
    {
        "title": "Machine Learning Algorithms Explained",
        "content": "<h2>Understanding ML Algorithms</h2><p>Machine learning algorithms are the backbone of AI systems. This article breaks down the most important algorithms every data scientist should know.</p><h3>Supervised Learning</h3><p>Supervised learning algorithms learn from labeled training data to make predictions on new, unseen data.</p><ul><li><strong>Linear Regression:</strong> Predicts continuous values</li><li><strong>Decision Trees:</strong> Makes decisions through a tree-like model</li><li><strong>Random Forest:</strong> Combines multiple decision trees</li></ul><h3>Unsupervised Learning</h3><p>These algorithms find hidden patterns in data without labeled examples.</p><ul><li><strong>K-Means Clustering:</strong> Groups similar data points</li><li><strong>PCA:</strong> Reduces data dimensionality</li></ul>",
        "category": "Machine Learning",
        "tags": ["Algorithms", "Data Science", "Supervised Learning", "Unsupervised Learning"],
        "author_name": "Prof. Michael Rodriguez",
        "featured_image": "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=400&fit=crop",
        "status": "published"
    },
    {
        "title": "Cloud Computing Best Practices",
        "content": "<h2>Mastering Cloud Infrastructure</h2><p>Cloud computing has revolutionized how businesses deploy and scale applications. Learn the essential best practices for cloud architecture.</p><h3>Key Cloud Principles</h3><ul><li>Scalability and elasticity</li><li>High availability and fault tolerance</li><li>Security and compliance</li><li>Cost optimization</li></ul><h3>Popular Cloud Platforms</h3><p>Each major cloud provider offers unique advantages:</p><ul><li><strong>AWS:</strong> Comprehensive service ecosystem</li><li><strong>Azure:</strong> Strong enterprise integration</li><li><strong>Google Cloud:</strong> Advanced AI/ML services</li></ul><p>Understanding these platforms will help you make informed decisions for your cloud strategy.</p>",
        "category": "Cloud Computing",
        "tags": ["AWS", "Azure", "Google Cloud", "DevOps", "Infrastructure"],
        "author_name": "Alex Thompson",
        "featured_image": "https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=400&fit=crop",
        "status": "published"
    },
    {
        "title": "Cybersecurity Fundamentals",
        "content": "<h2>Protecting Your Digital Assets</h2><p>In today's interconnected world, cybersecurity is more important than ever. This guide covers essential security practices every organization should implement.</p><h3>Common Threats</h3><ul><li>Malware and ransomware</li><li>Phishing attacks</li><li>Data breaches</li><li>Social engineering</li></ul><h3>Security Best Practices</h3><p>Implement these fundamental security measures:</p><ul><li><strong>Multi-factor authentication:</strong> Add extra layers of security</li><li><strong>Regular updates:</strong> Keep systems patched</li><li><strong>Employee training:</strong> Educate your team</li><li><strong>Backup strategies:</strong> Prepare for disasters</li></ul><p>Remember, security is not a one-time setup but an ongoing process.</p>",
        "category": "Cybersecurity",
        "tags": ["Security", "Privacy", "Malware", "Authentication", "Best Practices"],
        "author_name": "Jessica Park",
        "featured_image": "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=400&fit=crop",
        "status": "published"
    },
    {
        "title": "Modern Web Development Stack",
        "content": "<h2>Building Modern Web Applications</h2><p>The web development landscape is constantly evolving. Stay up-to-date with the latest technologies and frameworks that are shaping the future of web development.</p><h3>Frontend Technologies</h3><ul><li><strong>React:</strong> Component-based UI library</li><li><strong>Vue.js:</strong> Progressive JavaScript framework</li><li><strong>Angular:</strong> Full-featured framework</li><li><strong>Svelte:</strong> Compile-time optimized framework</li></ul><h3>Backend Solutions</h3><ul><li><strong>Node.js:</strong> JavaScript runtime for servers</li><li><strong>Python Django/Flask:</strong> Rapid development frameworks</li><li><strong>Go:</strong> Fast and efficient language</li></ul><p>Choose the right stack based on your project requirements and team expertise.</p>",
        "category": "Web Development",
        "tags": ["React", "JavaScript", "Frontend", "Backend", "Full Stack"],
        "author_name": "David Kim",
        "featured_image": "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop",
        "status": "published"
    },
    {
        "title": "Mobile App Development Trends",
        "content": "<h2>The Future of Mobile Development</h2><p>Mobile applications continue to dominate the digital landscape. Explore the latest trends and technologies in mobile app development.</p><h3>Cross-Platform Development</h3><ul><li><strong>React Native:</strong> Write once, run anywhere</li><li><strong>Flutter:</strong> Google's UI toolkit</li><li><strong>Xamarin:</strong> Microsoft's solution</li></ul><h3>Native Development</h3><p>Sometimes native development is the best choice:</p><ul><li><strong>iOS (Swift):</strong> Optimal performance on Apple devices</li><li><strong>Android (Kotlin):</strong> Modern language for Android</li></ul><h3>Emerging Technologies</h3><ul><li>5G connectivity</li><li>AR/VR integration</li><li>AI-powered features</li></ul>",
        "category": "Mobile Development",
        "tags": ["React Native", "Flutter", "iOS", "Android", "Cross Platform"],
        "author_name": "Maria Gonzalez",
        "featured_image": "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=400&fit=crop",
        "status": "published"
    }
]

async def create_sample_posts():
    """Create sample blog posts in the database"""
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    
    try:
        # Clear existing blog posts
        await db.blogs.delete_many({})
        print("Cleared existing blog posts")
        
        # Create sample posts
        for i, post_data in enumerate(SAMPLE_POSTS):
            # Generate unique ID and timestamps
            post_id = str(uuid.uuid4())
            created_at = datetime.utcnow() - timedelta(days=random.randint(1, 30))
            
            # Generate slug from title
            slug = post_data["title"].lower().replace(" ", "-").replace(",", "")
            slug = ''.join(c for c in slug if c.isalnum() or c == '-')[:50]
            
            # Calculate reading time (rough estimate)
            word_count = len(post_data["content"].split())
            reading_time = max(1, round(word_count / 200))
            
            # Generate excerpt
            text_content = post_data["content"].replace("<h2>", "").replace("</h2>", "").replace("<h3>", "").replace("</h3>", "").replace("<p>", "").replace("</p>", " ").replace("<ul>", "").replace("</ul>", "").replace("<li>", "").replace("</li>", " ").replace("<strong>", "").replace("</strong>", "")
            excerpt = text_content[:200].strip() + "..." if len(text_content) > 200 else text_content
            
            blog_post = {
                "id": post_id,
                "title": post_data["title"],
                "content": post_data["content"],
                "status": post_data["status"],
                "created_at": created_at,
                "updated_at": created_at,
                "author_id": None,
                "author_name": post_data["author_name"],
                "slug": slug,
                "category": post_data["category"],
                "tags": post_data["tags"],
                "featured_image": post_data["featured_image"],
                "excerpt": excerpt,
                "reading_time": reading_time,
                "view_count": random.randint(50, 500)
            }
            
            await db.blogs.insert_one(blog_post)
            print(f"Created blog post: {post_data['title']}")
        
        print(f"\nSuccessfully created {len(SAMPLE_POSTS)} sample blog posts!")
        
    except Exception as e:
        print(f"Error creating sample posts: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(create_sample_posts())
